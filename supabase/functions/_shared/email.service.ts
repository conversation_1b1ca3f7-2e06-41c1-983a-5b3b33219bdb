import SendGrid from "npm:@sendgrid/mail";

export enum EmailType {
  "SUBSCRIPTION_INVOICE_PAYMENT_FAILED",
  "SUBCONTRACTOR_CREATED",
}

type EmailAttachment = {
  content: string; // base64
  filename: string;
  type?: string;
  disposition?: string;
};

type SendEmailOptions = {
  to: string;
  from?: string;
  emailType: EmailType;
  subject?: string;
  html?: string; // allow custom html
  attachments?: EmailAttachment[]; // allow attachments
};

class EmailHtmlGenerator {
  constructor() {}

  private createSubscriptionInvoicePaymentFailedEmail(
    options: SendEmailOptions
  ): string {
    return `<html>Hey ${options.to}, your payment failed</html>`;
  }

  public createEmail(options: SendEmailOptions): string {
    switch (options.emailType) {
      case EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED:
        return this.createSubscriptionInvoicePaymentFailedEmail(options);
    }
  }
}

export class EmailService {
  private htmlGenerator: EmailHtmlGenerator;
  private sendGridApiKey: string;
  private senderEmail: string;

  constructor(sendGridApiKey: string, senderEmail: string) {
    this.sendGridApiKey = sendGridApiKey;
    this.senderEmail = senderEmail;
    this.htmlGenerator = new EmailHtmlGenerator();

    SendGrid.setApiKey(this.sendGridApiKey);
  }

  private getSubjectByEmailType(type: EmailType): string {
    switch (type) {
      case EmailType.SUBSCRIPTION_INVOICE_PAYMENT_FAILED:
        return "Your subscription invoice payment failed - need action";
      default:
        return "Notification from Pleasant Plumbers";
    }
  }

  private getSenderEmail(options: SendEmailOptions) {
    return options.from ?? this.senderEmail;
  }

  public async sendEmail(options: SendEmailOptions) {
    try {
      const response = await SendGrid.send({
        to: options.to,
        from: this.getSenderEmail(options),
        subject: options.subject || this.getSubjectByEmailType(options.emailType),
        html: options.html || this.htmlGenerator.createEmail(options),
        attachments: options.attachments,
      });

      return response;
    } catch (error) {
      return {
        error: {
          message: "Failed to send email",
          details: error,
        },
      };
    }
  }
}
